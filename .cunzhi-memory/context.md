# 项目上下文信息

- 项目技术栈：Vue.js + TypeScript + Pinia + 腾讯云IM SDK。现有弹窗组件：Dialog、BottomPopup、SlideUpModal。TUIChat组件体系完整，有ChatStorage聊天存储机制。需要实现历史对话弹窗功能。
- 用户需要分析比较两个IM消息数据结构差异：1)正常实时消息数据 2)历史记录接口转换后的消息数据，都是TIMCustomElem类型包含AI指令"/文生图"
- 已收到第一个数据对象（实时消息），包含"/图生图"指令，ID为144115248152653239-1753757018-95738565，等待第二个历史记录数据对象进行对比分析
- 用户需要实现 chatStore.historyMessages 变化时自动拼接到 messageList.value 前面的功能，需要考虑去重逻辑，消息对象使用 ID 字段作为唯一标识符
